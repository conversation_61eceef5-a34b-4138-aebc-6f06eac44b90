// src/components/BukuSection.tsx
import React from "react";

const BukuSection: React.FC = () => {
  return (
    <div className="mt-8">
      <h3 className="text-lg font-bold border-b-2 border-gray-200 pb-2 mb-4">
        BUKU
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-white shadow rounded overflow-hidden p-4">
          <div className="flex items-center">
            <img
              src="/images/book-1.jpg"
              alt="Book"
              className="w-24 h-32 object-cover mr-4"
            />
            <div>
              <h4 className="text-md font-bold mb-1">
                Peta Investasi Kota Pekanbaru 2025
              </h4>
              <p className="text-xs text-gray-500">Publikasi: Maret 2025</p>
              <button className="bg-blue-500 text-white px-3 py-1 rounded text-xs mt-2">
                Download
              </button>
            </div>
          </div>
        </div>
        <div className="bg-white shadow rounded overflow-hidden p-4">
          <div className="flex items-center">
            <img
              src="/images/book-2.jpg"
              alt="Book"
              className="w-24 h-32 object-cover mr-4"
            />
            <div>
              <h4 className="text-md font-bold mb-1">
                Panduan Perizinan Usaha di Pekanbaru
              </h4>
              <p className="text-xs text-gray-500">Publikasi: Januari 2025</p>
              <button className="bg-blue-500 text-white px-3 py-1 rounded text-xs mt-2">
                Download
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BukuSection;
