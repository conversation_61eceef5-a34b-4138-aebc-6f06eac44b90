// src/components/ChatWidget.tsx

"use client";

import React, { useEffect, useState, useRef } from "react";
import { ArrowLeft, Loader2, ChevronDown, X } from "lucide-react";
import socketIO from "socket.io-client";
import type { Socket } from "socket.io-client";
import { v4 as uuidv4 } from "uuid";
import "../app/globals.css";

type Message = {
  id: string;
  content: string;
  timestamp: string;
  role: "user" | "assistant";
};

const AVATAR_URL = "/images/operator1.png";

const ChatWidget: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [showInitialTerms, setShowInitialTerms] = useState(true);
  const [inputMessage, setInputMessage] = useState("");
  const [isConnected, setIsConnected] = useState(false);
  const [isBotTyping, setIsBotTyping] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [hasConsented, setHasConsented] = useState(false);
  const [hasRejectedTerms, setHasRejectedTerms] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [widgetAnimation, setWidgetAnimation] = useState("");
  const [buttonAnimation, setButtonAnimation] = useState("");
  const [indicatorAnimation, setIndicatorAnimation] = useState("");

  const socketRef = useRef<typeof Socket | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const messagesRef = useRef<Message[]>([]);
  const hasInitializedRef = useRef(false);
  const pendingMessageRef = useRef<string | null>(null);
  const scrollPositionRef = useRef<number>(0);
  const shouldRestoreScrollRef = useRef<boolean>(false);

  // Get or create chatId from localStorage - now with browser check
  const [chatId, setChatId] = useState<string>(() => {
    // Default value in case we're on the server
    const defaultId = uuidv4();

    // Only access localStorage in the browser
    if (typeof window !== "undefined") {
      const savedChatId = localStorage.getItem("chat-widget-id");
      const newChatId = savedChatId || defaultId;
      if (!savedChatId) localStorage.setItem("chat-widget-id", newChatId);
      return newChatId;
    }

    return defaultId;
  });

  // Initialize consent from localStorage - with browser check
  useEffect(() => {
    // This useEffect only runs in the browser
    if (typeof window !== "undefined") {
      const savedConsent = localStorage.getItem("chat-consent");
      if (savedConsent === "true") {
        setHasConsented(true);
        setShowInitialTerms(false);
      }
    }
  }, []);

  // Socket connection handling - now independent of isOpen state
  useEffect(() => {
    // Only create the connection if it doesn't exist yet and we're in the browser
    if (socketRef.current || typeof window === "undefined") return;

    const BACKEND_URL =
      process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:3000";
    socketRef.current = socketIO(BACKEND_URL);
    const socket = socketRef.current;

    socket.on("connect", () => {
      setIsConnected(true);
      if (!hasInitializedRef.current) {
        socket.emit("join-chat", chatId);
        socket.emit("check-terms-status", { chatId });
        hasInitializedRef.current = true;
      }
    });

    socket.on("disconnect", () => setIsConnected(false));
    socket.on("connect_error", () => setIsConnected(false));

    socket.on("terms-status", (data: { accepted: boolean }) => {
      setHasConsented(data.accepted);
      if (data.accepted) {
        setShowInitialTerms(false);
        setHasRejectedTerms(false);
      }
    });

    socket.on("chat-history", (history: Message[]) => {
      if (messagesRef.current.length === 0) {
        setMessages(history);
        messagesRef.current = history;

        // Mark that we should restore scroll position after component updates
        if (isOpen) {
          shouldRestoreScrollRef.current = true;
        }
      }
    });

    socket.on("new-messages", (newMessages: Message[]) => {
      setIsBotTyping(false);
      setMessages((prevMessages) => {
        const existingIds = new Set(prevMessages.map((msg) => msg.id));
        const filteredMessages = newMessages
          .filter(
            (msg) =>
              !existingIds.has(msg.id) &&
              !(
                msg.role === "user" && msg.content === pendingMessageRef.current
              )
          )
          .map((msg) => ({
            ...msg,
            content: msg.content.replace(/<think>.*?<\/think>/g, "").trim(),
          }));

        const updatedMessages = [...prevMessages, ...filteredMessages];
        messagesRef.current = updatedMessages;

        if (isOpen && isNearBottom()) {
          setTimeout(scrollToBottom, 100);
        }

        return updatedMessages;
      });
    });

    socket.on("chat-created", (newChatId: string) => {
      if (typeof window !== "undefined") {
        localStorage.setItem("chat-widget-id", newChatId);
      }
      setChatId(newChatId);
    });

    // Cleanup function - now only disconnects when component unmounts
    return () => {
      if (chatContainerRef.current) {
        scrollPositionRef.current = chatContainerRef.current.scrollTop;
      }
      socket.disconnect();
      socketRef.current = null;
      hasInitializedRef.current = false;
    };
  }, [chatId]); // No longer dependent on isOpen

  // Handle opening and closing of chat widget
  useEffect(() => {
    if (isOpen && socketRef.current) {
      // When widget opens, fetch latest chat history if needed
      socketRef.current.emit("get-chat-history", { chatId });
      // Mark that we should restore scroll position
      shouldRestoreScrollRef.current = true;
    }
  }, [isOpen, chatId]);

  // Effect to restore scroll position when chat reopens
  useEffect(() => {
    if (isOpen && shouldRestoreScrollRef.current && chatContainerRef.current) {
      // Use a short timeout to ensure the DOM has updated
      const timer = setTimeout(() => {
        if (chatContainerRef.current) {
          // If we have a saved position, restore to that position
          if (scrollPositionRef.current > 0) {
            chatContainerRef.current.scrollTop = scrollPositionRef.current;
          }
          shouldRestoreScrollRef.current = false;
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [isOpen, messages]);

  // Scroll button visibility
  useEffect(() => {
    const container = chatContainerRef.current;
    if (!container || !hasConsented || !isOpen) return;

    const handleScroll = () => {
      const distanceFromBottom =
        container.scrollHeight - container.scrollTop - container.clientHeight;
      setShowScrollButton(distanceFromBottom > 150);
    };

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [hasConsented, messages, isOpen]);

  // Auto-scroll when bot is typing
  useEffect(() => {
    if (isBotTyping && isOpen && isNearBottom()) {
      scrollToBottom();
    }
  }, [isBotTyping, isOpen]);

  // Save scroll position periodically
  useEffect(() => {
    if (isOpen && chatContainerRef.current) {
      const saveScrollPosition = () => {
        if (chatContainerRef.current) {
          scrollPositionRef.current = chatContainerRef.current.scrollTop;
        }
      };

      // Save position on scroll
      chatContainerRef.current.addEventListener("scroll", saveScrollPosition);

      return () => {
        if (chatContainerRef.current) {
          chatContainerRef.current.removeEventListener(
            "scroll",
            saveScrollPosition
          );
        }
      };
    }
  }, [isOpen]);

  // Helper functions
  const isNearBottom = () => {
    const container = chatContainerRef.current;
    if (!container) return true;
    return (
      container.scrollHeight - container.scrollTop - container.clientHeight <
      100
    );
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    setShowScrollButton(false);
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const toggleChat = () => {
    if (isOpen) {
      // Closing the chat - save current scroll position
      if (chatContainerRef.current) {
        scrollPositionRef.current = chatContainerRef.current.scrollTop;
      }

      // First animate the widget out
      setWidgetAnimation("animate-slide-down");
      setIndicatorAnimation(""); // Reset indicator animation
      setTimeout(() => {
        setIsOpen(false);
        setWidgetAnimation("");
        setButtonAnimation("animate-slide-up");
        setIndicatorAnimation("animate-slide-up"); // Apply same animation as button
      }, 300);
    } else {
      // Opening the chat
      setButtonAnimation("animate-slide-down");
      setIndicatorAnimation("animate-slide-down"); // Apply same animation as button
      setTimeout(() => {
        setIsOpen(true);
        setWidgetAnimation("animate-slide-up");
      }, 300);
    }
  };

  // Event handlers
  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputMessage.trim() || !isConnected || !hasConsented) return;

    const messageContent = inputMessage.trim();
    pendingMessageRef.current = messageContent;

    const newMessage: Message = {
      id: uuidv4(),
      content: messageContent,
      timestamp: new Date().toISOString(),
      role: "user",
    };

    setMessages((prev) => {
      const updatedMessages = [...prev, newMessage];
      messagesRef.current = updatedMessages;
      return updatedMessages;
    });

    setInputMessage("");
    setIsBotTyping(true);
    setTimeout(scrollToBottom, 100);

    socketRef.current?.emit("send-message", {
      chatId,
      content: messageContent,
    });
  };

  const handleAcceptTerms = () => {
    setIsInitializing(true);
    socketRef.current?.emit("accept-terms", { chatId });

    if (typeof window !== "undefined") {
      localStorage.setItem("chat-consent", "true");
    }

    setHasConsented(true);
    setShowInitialTerms(false);
    setHasRejectedTerms(false);
    setTimeout(() => setIsInitializing(false), 2000);
  };

  const handleRejectTerms = () => {
    setHasRejectedTerms(true);
    setHasConsented(false);

    if (typeof window !== "undefined") {
      localStorage.removeItem("chat-consent");
    }
  };

  // UI Components
  const TypingAnimation = () => (
    <div className="flex space-x-1 items-center justify-center">
      <div
        className="h-2 w-2 bg-gray-400 rounded-full animate-bounce"
        style={{ animationDelay: "0.1s" }}
      ></div>
      <div
        className="h-2 w-2 bg-gray-400 rounded-full animate-bounce"
        style={{ animationDelay: "0.2s" }}
      ></div>
      <div
        className="h-2 w-2 bg-gray-400 rounded-full animate-bounce"
        style={{ animationDelay: "0.3s" }}
      ></div>
    </div>
  );

  const LoadingState = () => (
    <div className="flex flex-col items-center justify-center h-full space-y-4">
      <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
      <p className="text-sm text-gray-600">
        {hasConsented
          ? "Menyiapkan asisten obrolan Anda..."
          : "Memuat antarmuka obrolan..."}
      </p>
    </div>
  );

  const VirtualAssistantIntroContent = () => (
    <div className="flex flex-col h-full">
      <div className="flex-1 mb-4 overflow-hidden flex flex-col">
        <div className="overflow-y-auto pr-4 flex-1 pb-2 hide-scrollbar">
          <div className="text-sm text-gray-900">
            <h3 className="font-bold text-base mb-3">
              Asisten Virtual DPMPTSP
            </h3>
            <p className="mb-3">
              Halo! Saya adalah asisten virtual DPMPTSP (Dinas Penanaman Modal
              dan Pelayanan Terpadu Satu Pintu) yang siap membantu Anda. Untuk
              dapat memberikan layanan yang optimal, kami memerlukan persetujuan
              Anda terkait penggunaan informasi pribadi:
            </p>
            <ol className="list-decimal pl-5 space-y-2 mb-3">
              <li>
                Kami akan menyimpan informasi percakapan Anda untuk meningkatkan
                kualitas layanan.
              </li>
              <li>
                Kami mungkin menghubungi Anda untuk menindaklanjuti permohonan
                layanan yang Anda ajukan.
              </li>
            </ol>
            <p className="font-medium">Apakah Anda bersedia melanjutkan?</p>
          </div>
        </div>
      </div>
      <div className="flex space-x-2 pb-2 mr-4">
        <button
          onClick={handleAcceptTerms}
          className="flex-1 p-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          Ya, Lanjutkan
        </button>
        <button
          onClick={handleRejectTerms}
          className="flex-1 p-3 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
        >
          Tidak Sekarang
        </button>
      </div>
    </div>
  );

  const AssistantRejectedContent = () => (
    <div className="flex flex-col h-full">
      <div className="flex-1 text-sm mb-4 pr-4">
        <div className="bg-yellow-50 rounded-lg p-4 mb-4">
          <h3 className="font-bold text-yellow-800 mb-2">
            Layanan Tidak Dilanjutkan
          </h3>
          <p className="text-yellow-700">
            Tidak masalah. Anda tetap dapat mengakses informasi umum tentang
            layanan DPMPTSP melalui website resmi kami. Anda dapat kembali kapan
            saja untuk menggunakan asisten virtual.
          </p>
        </div>
      </div>
      <div className="flex space-x-4 pb-2 mr-4">
        <button
          onClick={() => setHasRejectedTerms(false)}
          className="w-full p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Kembali
        </button>
      </div>
    </div>
  );

  // Determine if connection indicator should be visible
  // Only show when the chat is closed AND user has consented
  const showConnectionIndicator =
    hasConsented && (!isOpen || indicatorAnimation !== "");

  return (
    <>
      {/* Connection status indicator (with animation matching chat button) */}
      {showConnectionIndicator && (
        <div
          className={`fixed bottom-20 right-6 z-50 ${indicatorAnimation}`}
          style={{
            display: isOpen && indicatorAnimation === "" ? "none" : "block",
          }}
        >
          <div
            className={`flex items-center justify-center p-1 rounded-full ${
              isConnected ? "bg-green-500" : "bg-red-500"
            } w-3 h-3`}
          ></div>
        </div>
      )}

      {/* Chat button */}
      <button
        onClick={toggleChat}
        className={`fixed bottom-6 right-6 p-4 rounded-full shadow-lg z-50 transition-all bg-blue-500 hover:bg-blue-600 text-white ${buttonAnimation}`}
        aria-label="Toggle chat widget"
        style={{ display: isOpen && buttonAnimation === "" ? "none" : "block" }}
      >
        <div className="flex items-center justify-center w-6 h-6">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
          </svg>
        </div>
      </button>

      {/* Chat widget */}
      {(isOpen || widgetAnimation === "animate-slide-down") && (
        <div
          className={`fixed bottom-0 right-6 w-80 md:w-96 h-[520px] rounded-t-lg shadow-xl z-[100] flex flex-col overflow-hidden bg-white border border-gray-200 ${widgetAnimation}`}
        >
          {/* Header */}
          <div className="p-3 bg-blue-600 text-white border-b flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-blue-400 rounded-full overflow-hidden mr-2">
                <img
                  src={AVATAR_URL}
                  alt="Asisten Virtual"
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <h3 className="font-medium text-sm">Asisten Virtual DPMPTSP</h3>
                {hasConsented && (
                  <div className="flex items-center">
                    <div
                      className={`w-2 h-2 rounded-full mr-1 ${
                        isConnected ? "bg-green-400" : "bg-red-400"
                      }`}
                    ></div>
                    <span className="text-xs">
                      {isConnected ? "Online" : "Offline"}
                    </span>
                  </div>
                )}
              </div>
            </div>
            {/* Close button moved to the right side of header */}
            <button
              onClick={toggleChat}
              className="text-white hover:text-gray-200 transition-colors"
              aria-label="Close chat"
            >
              <X size={20} />
            </button>
          </div>

          {/* Chat body */}
          <div className="flex-1 pl-4 bg-gray-50 overflow-hidden relative">
            {isInitializing ? (
              <LoadingState />
            ) : !hasConsented && !hasRejectedTerms && showInitialTerms ? (
              <VirtualAssistantIntroContent />
            ) : hasRejectedTerms ? (
              <AssistantRejectedContent />
            ) : !isConnected ? (
              <div className="flex flex-col items-center justify-center h-full">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center max-w-xs">
                  <div className="text-red-500 mb-2">
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      className="mx-auto mb-2"
                    >
                      <line x1="18" y1="6" x2="6" y2="18"></line>
                      <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                  </div>
                  <h3 className="font-medium text-red-700 mb-1">
                    Koneksi Teputus
                  </h3>
                  <p className="text-sm text-red-600">
                    Chatbot saat ini sedang offline. Harap segarkan halaman atau
                    periksa koneksi internet Anda.
                  </p>
                </div>
              </div>
            ) : (
              <div
                className="h-full overflow-y-auto pr-4 chat-messages-container"
                ref={chatContainerRef}
              >
                {messages.length === 0 ? (
                  <div className="flex items-center justify-center h-full text-gray-500 text-sm">
                    Obrolan kosong. Ketik sesuatu untuk memulai percakapan!
                  </div>
                ) : (
                  messages.map((message) => (
                    <div
                      key={message.id}
                      className={`mb-4 ${
                        message.role === "user" ? "text-right" : "text-left"
                      }`}
                    >
                      {message.role === "assistant" ? (
                        <div className="flex items-start">
                          <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0 mr-2 mt-1">
                            <img
                              src={AVATAR_URL}
                              alt="AI Assistant"
                              className="w-full h-full object-cover bg-blue-400"
                            />
                          </div>
                          <div>
                            <div className="px-3 py-2 rounded-lg bg-gray-100 text-gray-800 text-sm">
                              <p className="whitespace-pre-wrap">
                                {message.content}
                              </p>
                            </div>
                            <div className="text-xs text-gray-500 ml-1 mt-1">
                              {formatTimestamp(message.timestamp)}
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div>
                          <div className="inline-block px-3 py-2 rounded-lg bg-blue-600 text-white text-sm">
                            <p className="whitespace-pre-wrap">
                              {message.content}
                            </p>
                          </div>
                          <div className="text-xs text-gray-500 text-right mt-1">
                            {formatTimestamp(message.timestamp)}
                          </div>
                        </div>
                      )}
                    </div>
                  ))
                )}
                {isBotTyping && (
                  <div className="mb-4 text-left">
                    <div className="flex items-start">
                      <div className="w-6 h-6 rounded-full overflow-hidden flex-shrink-0 mr-2 mt-1">
                        <img
                          src={AVATAR_URL}
                          alt="AI Assistant"
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div>
                        <div className="px-3 py-2 rounded-lg bg-gray-100 text-gray-800 text-sm">
                          <TypingAnimation />
                        </div>
                        <div className="text-xs text-gray-500 ml-1 mt-1">
                          {formatTimestamp(new Date().toISOString())}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
            )}

            {/* Scroll to bottom button */}
            {hasConsented && showScrollButton && (
              <button
                onClick={scrollToBottom}
                className="absolute bottom-2 left-1/2 transform -translate-x-1/2 rounded-full bg-gray-800 bg-opacity-70 hover:bg-opacity-90 text-white p-2 transition-all shadow-md"
                aria-label="Scroll to bottom"
              >
                <ChevronDown size={20} />
              </button>
            )}
          </div>

          {/* Chat input - Now only displayed when connected */}
          {hasConsented && !isInitializing && isConnected && (
            <form
              onSubmit={handleSendMessage}
              className="p-3 border-t border-gray-200 bg-white"
            >
              <div className="flex items-center bg-gray-100 rounded-full px-3 py-1">
                <input
                  type="text"
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  placeholder="Type a reply..."
                  className="flex-1 p-2 bg-transparent border-none focus:outline-none text-sm text-gray-900"
                />
                <button type="submit" className="p-2 text-blue-600">
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                  >
                    <path d="m22 2-7 20-4-9-9-4Z" />
                    <path d="M22 2 11 13" />
                  </svg>
                </button>
              </div>
            </form>
          )}
        </div>
      )}
    </>
  );
};

export default ChatWidget;
