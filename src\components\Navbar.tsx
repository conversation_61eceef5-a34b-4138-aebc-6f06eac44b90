"use client";

import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

const Navbar = () => {
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
    const [expandedMobileItems, setExpandedMobileItems] = useState<{ [key: string]: boolean }>({});

    const toggleMobileMenu = () => {
      setMobileMenuOpen(!mobileMenuOpen);
    };

    const toggleMobileItem = (id: string) => {
      setExpandedMobileItems({
        ...expandedMobileItems,
        [id]: !expandedMobileItems[id],
      });
    };

  const menuItems = [
    {
      id: "home",
      label: "Home",
      href: "/",
      dropdown: false,
      active: false,
    },
    {
      id: "profile",
      label: "Profile",
      href: "",
      dropdown: true,
      active: false,
      subItems: [
        { id: "profile-main", label: "Profile", href: "/profile" },
        { id: "visi-misi", label: "Visi Misi", href: "/profile/visi-misi" },
        {
          id: "struktur",
          label: "Struktur Organisasi",
          href: "/profile/struktur",
        },
        { id: "gallery", label: "Gallery Kegiatan", href: "/profile/gallery" },
      ],
    },
    {
      id: "zona-integritas",
      label: "Zona Integritas",
      href: "",
      dropdown: true,
      active: false,
      subItems: [
        {
          id: "maklumat",
          label: "Maklumat",
          href: "/zona-integritas/maklumat",
        },
        {
          id: "panduan",
          label: "Panduan Berlaku",
          href: "/zona-integritas/panduan-berlaku",
        },
      ],
    },
    {
      id: "berita",
      label: "Berita",
      href: "",
      dropdown: true,
      active: false,
      isFullWidthPanel: true,
    },
    {
      id: "promosi",
      label: "Promosi",
      href: "/promosi",
      dropdown: false,
      active: false,
    },
    {
      id: "pelayanan-perizinan",
      label: "Pelayanan Perizinan",
      href: "",
      dropdown: true,
      active: false,
      isFullWidthPanel: true,
    },
    {
      id: "file-publikasi",
      label: "File Publikasi",
      href: "/file-publikasi",
      dropdown: false,
      active: false,
    },
    {
      id: "contact",
      label: "Contact Us",
      href: "/contact",
      dropdown: false,
      active: false,
    },
  ];

  // Data for Berita panel - Updated to match image 2
  const beritaPanel = {
    sections: [
      {
        id: "informasi",
        title: "INFORMASI",
        items: [
          {
            id: "jangan-mudik",
            label: "Jangan Mudik (lagi)",
            date: "07/05/2021 14:37:42",
            href: "/informasi/jangan-mudik",
          },
          {
            id: "cara-pendaftaran",
            label:
              "Cara Melakukan Pendaftaran OSS untuk non perseorangan (CV maupun PT)",
            date: "07/05/2021 14:34:03",
            href: "/informasi/cara-pendaftaran-oss",
          },
          {
            id: "kunjungan-mpp",
            label: "Kunjungan MPP Masih Berkisar 400 Orang Dalam Sehari",
            date: "23/11/2020 14:55:19",
            href: "/informasi/kunjungan-mpp",
          },
          {
            id: "jam-pelayanan",
            label: "JAM PELAYANAN MAL PELAYANAN PUBLIK SELAMA RAMADHAN 1440 H",
            date: "06/05/2019 09:02:56",
            href: "/informasi/jam-pelayanan-ramadhan",
          },
          {
            id: "surat-edaran",
            label:
              "Surat Edaran Tentang Pendaftaran Perizinan dan Non Perizinan Pada DPMPTSP yang diterbitkan oleh OSS",
            date: "31/10/2018 12:41:38",
            href: "/informasi/surat-edaran-pendaftaran",
          },
        ],
      },
      {
        id: "recent-posts",
        title: "RECENT POSTS",
        hasArticles: true,
        articles: [
          {
            id: "article-1",
            title:
              "Pembangunan Gedung Utama MPP Pekanbaru Tunggu Walikota Terpilih",
            date: "22 Januari 2025",
            image: "/images/article1.jpg",
          },
          {
            id: "article-2",
            title:
              "Kepala DPMPTSP Pekanbaru Ikuti Apel Gabungan dan Pengarahan PJ Walikota",
            date: "31 Juli 2024",
            image: "/images/article2.jpg",
          },
          {
            id: "article-3",
            title:
              "PJ Wali Kota Pekanbaru Tinjau Pelayanan MPP Pasca Serangan Hacker ke PDNS 2",
            date: "07 Juli 2024",
            image: "/images/article3.jpg",
          },
        ],
      },
      {
        id: "archive",
        title: "ARCHIVE",
        items: [
          {
            id: "feb",
            label: "Februari (7)",
            href: "/berita/archive/februari",
          },
          { id: "jan", label: "Januari (3)", href: "/berita/archive/januari" },
          { id: "okt", label: "Oktober (2)", href: "/berita/archive/oktober" },
          {
            id: "sep",
            label: "September (3)",
            href: "/berita/archive/september",
          },
          { id: "aug", label: "Agustus (11)", href: "/berita/archive/agustus" },
          { id: "jul", label: "Juli (4)", href: "/berita/archive/juli" },
        ],
      },
      {
        id: "tags",
        title: "TAGS",
        hasTags: true,
        tags: [
          { id: "berita", label: "Berita", href: "/berita/tag/berita" },
          {
            id: "penghargaan",
            label: "Penghargaan",
            href: "/berita/tag/penghargaan",
          },
          {
            id: "kunjungan",
            label: "Kunjungan",
            href: "/berita/tag/kunjungan",
          },
          { id: "umkm", label: "UMKM", href: "/berita/tag/umkm" },
          {
            id: "informasi",
            label: "Informasi",
            href: "/berita/tag/informasi",
          },
          {
            id: "perizinan",
            label: "Perizinan",
            href: "/berita/tag/perizinan",
          },
          {
            id: "peluang-investasi",
            label: "Peluang Investasi",
            href: "/berita/tag/peluang-investasi",
          },
          {
            id: "penanaman-modal",
            label: "Penanaman Modal",
            href: "/berita/tag/penanaman-modal",
          },
        ],
      },
    ],
  };

  // Data for Pelayanan Perizinan panel - Updated to match image 1
  const perizinanPanel = {
    sections: [
      {
        id: "informasi",
        title: "INFORMASI",
        items: [
          {
            id: "jenis-perizinan",
            label: "Jenis Perizinan",
            href: "/pelayanan-perizinan/informasi/jenis-perizinan",
          },
          {
            id: "perizinan-online",
            label: "Perizinan Online",
            href: "/pelayanan-perizinan/informasi/perizinan-online",
          },
          {
            id: "alur-pelayanan",
            label: "Alur Pelayanan",
            href: "/pelayanan-perizinan/informasi/alur-pelayanan",
          },
          {
            id: "alur-pelayanan-online",
            label: "Alur Pelayanan Online",
            href: "/pelayanan-perizinan/informasi/alur-pelayanan-online",
          },
          {
            id: "alur-pengaduan",
            label: "Alur Pengaduan",
            href: "/pelayanan-perizinan/informasi/alur-pengaduan",
          },
          {
            id: "survei-kepuasan",
            label: "Survei Kepuasan Masyarakat",
            href: "/pelayanan-perizinan/informasi/survei-kepuasan",
          },
          {
            id: "data-statistik",
            label: "Data dan Statistik",
            href: "/pelayanan-perizinan/informasi/data-statistik",
          },
        ],
      },
      {
        id: "empty-section",
        title: "",
        items: [
          {
            id: "kswpd",
            label: "KONFIRMASI STATUS WAJIB PAJAK DAERAH (KSWPD)",
            href: "/pelayanan-perizinan/kswpd",
          },
          {
            id: "selengkapnya-general",
            label: "SELENGKAPNYA",
            href: "/pelayanan-perizinan/selengkapnya",
          },
        ],
      },
      {
        id: "kesehatan",
        title: "KESEHATAN",
        items: [
          {
            id: "siktg",
            label: "SURAT IZIN KERJA TEKNISI GIGI (SIKTG)",
            href: "/pelayanan-perizinan/kesehatan/siktg",
          },
          {
            id: "tukang-gigi",
            label: "SURAT IZIN TUKANG GIGI",
            href: "/pelayanan-perizinan/kesehatan/tukang-gigi",
          },
          {
            id: "sipa",
            label: "SURAT IZIN PRAKTIK APOTEKER (SIPA)",
            href: "/pelayanan-perizinan/kesehatan/sipa",
          },
          {
            id: "radiografer",
            label: "SURAT IZIN KERJA RADIOGRAFER",
            href: "/pelayanan-perizinan/kesehatan/radiografer",
          },
          {
            id: "sikro",
            label: "SURAT IZIN KERJA REFRAKSIONIS OPTISIEN (SIKRO)",
            href: "/pelayanan-perizinan/kesehatan/sikro",
          },
          {
            id: "selengkapnya-kesehatan",
            label: "SELENGKAPNYA",
            href: "/pelayanan-perizinan/kesehatan/selengkapnya",
          },
        ],
      },
      {
        id: "pariwisata",
        title: "PARIWISATA",
        items: [
          {
            id: "selengkapnya-pariwisata",
            label: "SELENGKAPNYA",
            href: "/pelayanan-perizinan/pariwisata/selengkapnya",
          },
        ],
      },
    ],
  };

  return (
    <div className="sticky top-0 z-50 bg-white text-gray-800 shadow-md">
      <nav className="border-b-2 border-red-500">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center py-4">
            {/* Logo */}
            <div className="flex items-center">
              <Link href="/">
                <div className="flex items-center">
                  <Image
                    src="/images/logodpmptsp1.png"
                    alt="DPMPTSP Pekanbaru Logo"
                    width={180}
                    height={60}
                    className="mr-2"
                  />
                </div>
              </Link>
            </div>

            {/* Mobile menu button */}
            <div className="block lg:hidden">
              <button
                onClick={toggleMobileMenu}
                className="flex items-center px-3 py-2 border rounded text-gray-800 border-gray-400 hover:text-red-500 hover:border-red-500"
              >
                <svg
                  className="fill-current h-5 w-5"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Menu</title>
                  <path d="M0 3h20v2H0V3zm0 6h20v2H0V9zm0 6h20v2H0v-2z" />
                </svg>
              </button>
            </div>

            {/* Main Navigation */}
            <div className="hidden lg:flex space-x-4-x-4">
              {menuItems.map((item) => (
                <div
                  key={item.id}
                  id={`menu-item-${item.id}`}
                  className="relative group"
                  onMouseEnter={() =>
                    item.dropdown && setActiveDropdown(item.id)
                  }
                  onMouseLeave={() => setActiveDropdown(null)}
                >
                  <Link
                    href={item.href}
                    className={`px-2 py-4 flex items-center ${
                      item.id === activeDropdown ? "text-red-500" : ""
                    } hover:text-red-500 transition-colors`}
                  >
                    <span>{item.label}</span>
                    {item.dropdown && (
                      <svg
                        className="ml-1 w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M19 9l-7 7-7-7"
                        ></path>
                      </svg>
                    )}
                  </Link>

                  {/* Regular dropdown for Profile and Zona Integritas */}
                  {item.dropdown &&
                    !item.isFullWidthPanel &&
                    activeDropdown === item.id && (
                      <div className="absolute top-full left-0 bg-white shadow-lg rounded-md py-2 min-w-[200px] z-10 border-t border-gray-200">
                        {item.subItems?.map((subItem) => (
                          <Link
                            key={subItem.id}
                            href={subItem.href}
                            className="block px-4 py-2 text-gray-700 hover:bg-gray-100"
                          >
                            {subItem.label}
                          </Link>
                        ))}
                      </div>
                    )}

                  {/* Full-width Berita dropdown panel - Updated to match image 2 */}
                  {item.id === "berita" && activeDropdown === "berita" && (
                    <div className="fixed left-0 right-0 w-full bg-white shadow-lg z-20 border-t border-gray-200">
                      <div className="container mx-auto">
                        <div className="grid grid-cols-4 gap-4 p-6">
                          {/* Informasi column */}
                          <div className="col-span-1 bg-gray-100 pl-4 pt-4">
                            <h3 className="text-sm font-bold mb-4 text-gray-700">
                              {beritaPanel.sections[0].title}
                            </h3>
                            <ul className="space-y-6">
                              {beritaPanel.sections[0].items?.map((item) => (
                                <li
                                  key={item.id}
                                  className="border-b border-gray-200 pb-4"
                                >
                                  <Link
                                    href={item.href}
                                    className="text-sm text-gray-700 hover:text-red-500"
                                  >
                                    {item.label}
                                  </Link>
                                </li>
                              ))}
                            </ul>
                          </div>

                          {/* Recent Posts column */}
                          <div className="col-span-1 pl-4 pt-4">
                            <h3 className="text-sm font-bold mb-4 text-gray-700">
                              {beritaPanel.sections[1].title}
                            </h3>
                            <div className="space-y-4">
                              {beritaPanel.sections[1].articles?.map(
                                (article) => (
                                  <div
                                    key={article.id}
                                    className="flex space-x-3"
                                  >
                                    <div className="w-16 h-16 bg-gray-200 flex-shrink-0">
                                      <Image
                                        src={
                                          article.image ||
                                          "/images/placeholder.jpg"
                                        }
                                        alt={article.title}
                                        width={64}
                                        height={64}
                                        className="object-cover w-full h-full"
                                      />
                                    </div>
                                    <div>
                                      <Link
                                        href={`/berita/${article.id}`}
                                        className="text-xs font-medium hover:text-red-500"
                                      >
                                        {article.title}
                                      </Link>
                                      <p className="text-xs text-gray-500 mt-1">
                                        {article.date}
                                      </p>
                                    </div>
                                  </div>
                                )
                              )}
                            </div>
                          </div>

                          {/* Archive column */}
                          <div className="col-span-1 bg-gray-100 pl-4 pt-4">
                            <h3 className="text-sm font-bold mb-4 text-gray-700">
                              {beritaPanel.sections[2].title}
                            </h3>
                            <ul className="space-y-2">
                              {beritaPanel.sections[2].items?.map((item) => (
                                <li key={item.id}>
                                  <Link
                                    href={item.href}
                                    className="text-sm text-gray-600 hover:text-red-500"
                                  >
                                    {item.label}
                                  </Link>
                                </li>
                              ))}
                            </ul>
                          </div>

                          {/* Tags column */}
                          <div className="col-span-1 pl-4 pt-4">
                            <h3 className="text-sm font-bold mb-4 text-gray-700">
                              {beritaPanel.sections[3].title}
                            </h3>
                            <div className="flex flex-wrap gap-2">
                              {beritaPanel.sections[3].tags?.map((tag) => (
                                <Link
                                  key={tag.id}
                                  href={tag.href}
                                  className="inline-block bg-gray-800 text-white text-xs px-3 py-1 rounded hover:bg-gray-700 mb-2"
                                >
                                  {tag.label}
                                </Link>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Full-width Pelayanan Perizinan dropdown panel - Updated to match image 1 */}
                  {item.id === "pelayanan-perizinan" &&
                    activeDropdown === "pelayanan-perizinan" && (
                      <div className="fixed left-0 right-0 w-full bg-white shadow-lg z-20 border-t border-gray-200">
                        <div className="container mx-auto">
                          <div className="grid grid-cols-4 gap-4 p-6">
                            {/* Informasi column */}
                            <div className="col-span-1 bg-gray-100 pl-4 pt-4">
                              <h3 className="text-sm font-bold mb-4 text-gray-700">
                                INFORMASI
                              </h3>
                              <ul className="space-y-2">
                                <li>
                                  <Link
                                    href="/pelayanan-perizinan/informasi/jenis-perizinan"
                                    className="text-sm text-gray-600 hover:text-red-500 flex"
                                  >
                                    <span className="text-gray-400 mr-2">
                                      ›
                                    </span>{" "}
                                    Jenis Perizinan
                                  </Link>
                                </li>
                                <li>
                                  <Link
                                    href="/pelayanan-perizinan/informasi/perizinan-online"
                                    className="text-sm text-gray-600 hover:text-red-500 flex"
                                  >
                                    <span className="text-gray-400 mr-2">
                                      ›
                                    </span>{" "}
                                    Perizinan Online
                                  </Link>
                                </li>
                                <li>
                                  <Link
                                    href="/pelayanan-perizinan/informasi/alur-pelayanan"
                                    className="text-sm text-gray-600 hover:text-red-500 flex"
                                  >
                                    <span className="text-gray-400 mr-2">
                                      ›
                                    </span>{" "}
                                    Alur Pelayanan
                                  </Link>
                                </li>
                                <li>
                                  <Link
                                    href="/pelayanan-perizinan/informasi/alur-pelayanan-online"
                                    className="text-sm text-gray-600 hover:text-red-500 flex"
                                  >
                                    <span className="text-gray-400 mr-2">
                                      ›
                                    </span>{" "}
                                    Alur Pelayanan Online
                                  </Link>
                                </li>
                                <li>
                                  <Link
                                    href="/pelayanan-perizinan/informasi/alur-pengaduan"
                                    className="text-sm text-gray-600 hover:text-red-500 flex"
                                  >
                                    <span className="text-gray-400 mr-2">
                                      ›
                                    </span>{" "}
                                    Alur Pengaduan
                                  </Link>
                                </li>
                                <li>
                                  <Link
                                    href="/pelayanan-perizinan/informasi/survei-kepuasan"
                                    className="text-sm text-gray-600 hover:text-red-500 flex"
                                  >
                                    <span className="text-gray-400 mr-2">
                                      ›
                                    </span>{" "}
                                    Survei Kepuasan Masyarakat
                                  </Link>
                                </li>
                                <li>
                                  <Link
                                    href="/pelayanan-perizinan/informasi/data-statistik"
                                    className="text-sm text-gray-600 hover:text-red-500 flex"
                                  >
                                    <span className="text-gray-400 mr-2">
                                      ›
                                    </span>{" "}
                                    Data dan Statistik
                                  </Link>
                                </li>
                              </ul>
                            </div>

                            {/* KSWPD column */}
                            <div className="col-span-1 pl-4 pt-14">
                              <Link
                                href="/pelayanan-perizinan/kswpd"
                                className="text-sm text-gray-600 hover:text-red-500 block mb-4"
                              >
                                <span className="text-gray-400 mr-2">›</span>{" "}
                                KONFIRMASI STATUS WAJIB PAJAK DAERAH (KSWPD)
                              </Link>
                              <Link
                                href="/pelayanan-perizinan/selengkapnya"
                                className="text-sm text-gray-600 hover:text-red-500 block"
                              >
                                <span className="text-gray-400 mr-2">›</span>{" "}
                                SELENGKAPNYA
                              </Link>
                            </div>

                            {/* Kesehatan column */}
                            <div className="col-span-1 bg-gray-100 pl-4 pt-4">
                              <h3 className="text-sm font-bold mb-4 text-gray-700">
                                KESEHATAN
                              </h3>
                              <ul className="space-y-2">
                                <li>
                                  <Link
                                    href="/pelayanan-perizinan/kesehatan/siktg"
                                    className="text-sm text-gray-600 hover:text-red-500 flex"
                                  >
                                    <span className="text-gray-400 mr-2">
                                      ›
                                    </span>{" "}
                                    SURAT IZIN KERJA TEKNISI GIGI (SIKTG)
                                  </Link>
                                </li>
                                <li>
                                  <Link
                                    href="/pelayanan-perizinan/kesehatan/tukang-gigi"
                                    className="text-sm text-gray-600 hover:text-red-500 flex"
                                  >
                                    <span className="text-gray-400 mr-2">
                                      ›
                                    </span>{" "}
                                    SURAT IZIN TUKANG GIGI
                                  </Link>
                                </li>
                                <li>
                                  <Link
                                    href="/pelayanan-perizinan/kesehatan/sipa"
                                    className="text-sm text-gray-600 hover:text-red-500 flex"
                                  >
                                    <span className="text-gray-400 mr-2">
                                      ›
                                    </span>{" "}
                                    SURAT IZIN PRAKTIK APOTEKER (SIPA)
                                  </Link>
                                </li>
                                <li>
                                  <Link
                                    href="/pelayanan-perizinan/kesehatan/radiografer"
                                    className="text-sm text-gray-600 hover:text-red-500 flex"
                                  >
                                    <span className="text-gray-400 mr-2">
                                      ›
                                    </span>{" "}
                                    SURAT IZIN KERJA RADIOGRAFER
                                  </Link>
                                </li>
                                <li>
                                  <Link
                                    href="/pelayanan-perizinan/kesehatan/sikro"
                                    className="text-sm text-gray-600 hover:text-red-500 flex"
                                  >
                                    <span className="text-gray-400 mr-2">
                                      ›
                                    </span>{" "}
                                    SURAT IZIN KERJA REFRAKSIONIS OPTISIEN
                                    (SIKRO)
                                  </Link>
                                </li>
                                <li>
                                  <Link
                                    href="/pelayanan-perizinan/kesehatan/selengkapnya"
                                    className="text-sm text-gray-600 hover:text-red-500 flex"
                                  >
                                    <span className="text-gray-400 mr-2">
                                      ›
                                    </span>{" "}
                                    SELENGKAPNYA
                                  </Link>
                                </li>
                              </ul>
                            </div>

                            {/* Pariwisata column */}
                            <div className="col-span-1 pl-4 pt-4">
                              <h3 className="text-sm font-bold mb-4 text-gray-700">
                                PARIWISATA
                              </h3>
                              <Link
                                href="/pelayanan-perizinan/pariwisata/selengkapnya"
                                className="text-sm text-gray-600 hover:text-red-500 flex"
                              >
                                <span className="text-gray-400 mr-2">›</span>{" "}
                                SELENGKAPNYA
                              </Link>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Menu */}
      <div className={`${mobileMenuOpen ? "block" : "hidden"} lg:hidden`}>
        <div className="bg-white w-full shadow-lg">
          <div className="container mx-auto px-4">
            <ul>
              <li className="border-b border-gray-200">
                <Link
                  href="/"
                  className="block py-3 px-2 text-gray-800 hover:text-red-500"
                >
                  Home
                </Link>
              </li>

              <li className="border-b border-gray-200">
                <div className="flex justify-between items-center py-3 px-2">
                  <span className="text-gray-800">Profile</span>
                  <button
                    onClick={() => toggleMobileItem("profile")}
                    className="text-gray-500 focus:outline-none"
                  >
                    <svg
                      className={`w-5 h-5 transition-transform ${
                        expandedMobileItems["profile"]
                          ? "transform rotate-180"
                          : ""
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M19 9l-7 7-7-7"
                      ></path>
                    </svg>
                  </button>
                </div>
                {expandedMobileItems["profile"] && (
                  <ul className="pl-4 bg-gray-50">
                    <li>
                      <Link
                        href="/profile"
                        className="block py-2 px-2 text-gray-700 hover:text-red-500"
                      >
                        Profile
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="/profile/visi-misi"
                        className="block py-2 px-2 text-gray-700 hover:text-red-500"
                      >
                        Visi Misi
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="/profile/struktur"
                        className="block py-2 px-2 text-gray-700 hover:text-red-500"
                      >
                        Struktur Organisasi
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="/profile/gallery"
                        className="block py-2 px-2 text-gray-700 hover:text-red-500"
                      >
                        Gallery Kegiatan
                      </Link>
                    </li>
                  </ul>
                )}
              </li>

              <li className="border-b border-gray-200">
                <div className="flex justify-between items-center py-3 px-2">
                  <span className="text-gray-800">Zona Integritas</span>
                  <button
                    onClick={() => toggleMobileItem("zona-integritas")}
                    className="text-gray-500 focus:outline-none"
                  >
                    <svg
                      className={`w-5 h-5 transition-transform ${
                        expandedMobileItems["zona-integritas"]
                          ? "transform rotate-180"
                          : ""
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M19 9l-7 7-7-7"
                      ></path>
                    </svg>
                  </button>
                </div>
                {expandedMobileItems["zona-integritas"] && (
                  <ul className="pl-4 bg-gray-50">
                    <li>
                      <Link
                        href="/zona-integritas/maklumat"
                        className="block py-2 px-2 text-gray-700 hover:text-red-500"
                      >
                        Maklumat
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="/zona-integritas/panduan-berlaku"
                        className="block py-2 px-2 text-gray-700 hover:text-red-500"
                      >
                        Panduan Berlaku
                      </Link>
                    </li>
                  </ul>
                )}
              </li>

              <li className="border-b border-gray-200">
                <div className="flex justify-between items-center py-3 px-2">
                  <span className="text-gray-800">Berita</span>
                  <button
                    onClick={() => toggleMobileItem("berita")}
                    className="text-gray-500 focus:outline-none"
                  >
                    <svg
                      className={`w-5 h-5 transition-transform ${
                        expandedMobileItems["berita"]
                          ? "transform rotate-180"
                          : ""
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M19 9l-7 7-7-7"
                      ></path>
                    </svg>
                  </button>
                </div>
                {expandedMobileItems["berita"] && (
                  <ul className="pl-4 bg-gray-50">
                    <li className="py-2 px-2">
                      <h3 className="font-bold text-gray-700 mb-2">
                        INFORMASI
                      </h3>
                      <ul className="space-y-2 pl-2">
                        <li>
                          <Link
                            href="/informasi/jangan-mudik"
                            className="block text-gray-600 hover:text-red-500"
                          >
                            Jangan Mudik (lagi)
                          </Link>
                        </li>
                        <li>
                          <Link
                            href="/informasi/cara-pendaftaran-oss"
                            className="block text-gray-600 hover:text-red-500"
                          >
                            Cara Melakukan Pendaftaran OSS
                          </Link>
                        </li>
                        <li>
                          <Link
                            href="/informasi/kunjungan-mpp"
                            className="block text-gray-600 hover:text-red-500"
                          >
                            Kunjungan MPP
                          </Link>
                        </li>
                      </ul>
                    </li>
                    <li className="py-2 px-2">
                      <h3 className="font-bold text-gray-700 mb-2">ARCHIVE</h3>
                      <ul className="space-y-2 pl-2">
                        <li>
                          <Link
                            href="/berita/archive/februari"
                            className="block text-gray-600 hover:text-red-500"
                          >
                            Februari (7)
                          </Link>
                        </li>
                        <li>
                          <Link
                            href="/berita/archive/januari"
                            className="block text-gray-600 hover:text-red-500"
                          >
                            Januari (3)
                          </Link>
                        </li>
                      </ul>
                    </li>
                    <li className="py-2 px-2">
                      <h3 className="font-bold text-gray-700 mb-2">TAGS</h3>
                      <div className="flex flex-wrap gap-2">
                        <Link
                          href="/berita/tag/berita"
                          className="inline-block bg-gray-800 text-white text-xs px-2 py-1 rounded"
                        >
                          Berita
                        </Link>
                        <Link
                          href="/berita/tag/penghargaan"
                          className="inline-block bg-gray-800 text-white text-xs px-2 py-1 rounded"
                        >
                          Penghargaan
                        </Link>
                        <Link
                          href="/berita/tag/kunjungan"
                          className="inline-block bg-gray-800 text-white text-xs px-2 py-1 rounded"
                        >
                          Kunjungan
                        </Link>
                      </div>
                    </li>
                  </ul>
                )}
              </li>

              <li className="border-b border-gray-200">
                <Link
                  href="/promosi"
                  className="block py-3 px-2 text-gray-800 hover:text-red-500"
                >
                  Promosi
                </Link>
              </li>

              <li className="border-b border-gray-200">
                <div className="flex justify-between items-center py-3 px-2">
                  <span className="text-gray-800">Pelayanan Perizinan</span>
                  <button
                    onClick={() => toggleMobileItem("pelayanan-perizinan")}
                    className="text-gray-500 focus:outline-none"
                  >
                    <svg
                      className={`w-5 h-5 transition-transform ${
                        expandedMobileItems["pelayanan-perizinan"]
                          ? "transform rotate-180"
                          : ""
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M19 9l-7 7-7-7"
                      ></path>
                    </svg>
                  </button>
                </div>
                {expandedMobileItems["pelayanan-perizinan"] && (
                  <ul className="pl-4 bg-gray-50">
                    <li className="py-2 px-2">
                      <h3 className="font-bold text-gray-700 mb-2">
                        INFORMASI
                      </h3>
                      <ul className="space-y-2 pl-2">
                        <li>
                          <Link
                            href="/pelayanan-perizinan/informasi/jenis-perizinan"
                            className="block text-gray-600 hover:text-red-500"
                          >
                            <span className="text-gray-400 mr-1">›</span> Jenis
                            Perizinan
                          </Link>
                        </li>
                        <li>
                          <Link
                            href="/pelayanan-perizinan/informasi/perizinan-online"
                            className="block text-gray-600 hover:text-red-500"
                          >
                            <span className="text-gray-400 mr-1">›</span>{" "}
                            Perizinan Online
                          </Link>
                        </li>
                        <li>
                          <Link
                            href="/pelayanan-perizinan/informasi/alur-pelayanan"
                            className="block text-gray-600 hover:text-red-500"
                          >
                            <span className="text-gray-400 mr-1">›</span> Alur
                            Pelayanan
                          </Link>
                        </li>
                      </ul>
                    </li>
                    <li className="py-2 px-2">
                      <Link
                        href="/pelayanan-perizinan/kswpd"
                        className="block text-gray-600 hover:text-red-500"
                      >
                        <span className="text-gray-400 mr-1">›</span> KONFIRMASI
                        STATUS WAJIB PAJAK
                      </Link>
                    </li>
                    <li className="py-2 px-2">
                      <h3 className="font-bold text-gray-700 mb-2">
                        KESEHATAN
                      </h3>
                      <ul className="space-y-2 pl-2">
                        <li>
                          <Link
                            href="/pelayanan-perizinan/kesehatan/siktg"
                            className="block text-gray-600 hover:text-red-500"
                          >
                            <span className="text-gray-400 mr-1">›</span> SURAT
                            IZIN KERJA TEKNISI GIGI
                          </Link>
                        </li>
                        <li>
                          <Link
                            href="/pelayanan-perizinan/kesehatan/tukang-gigi"
                            className="block text-gray-600 hover:text-red-500"
                          >
                            <span className="text-gray-400 mr-1">›</span> SURAT
                            IZIN TUKANG GIGI
                          </Link>
                        </li>
                      </ul>
                    </li>
                  </ul>
                )}
              </li>

              <li className="border-b border-gray-200">
                <Link
                  href="/file-publikasi"
                  className="block py-3 px-2 text-gray-800 hover:text-red-500"
                >
                  File Publikasi
                </Link>
              </li>

              <li className="border-b border-gray-200">
                <Link
                  href="/contact"
                  className="block py-3 px-2 text-gray-800 hover:text-red-500"
                >
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Navbar;
