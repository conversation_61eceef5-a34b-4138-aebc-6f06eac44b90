// src/app/page.tsx
"use client";

import React from "react";
import TopBanner from "@/components/main/TopBanner";
import MainFeature from "@/components/main/MainFeature";
import LatestNewsPosts from "@/components/main/LatestNewsPosts";
import PromoInvestasi from "@/components/main/PromoInvestasi";
import Sidebar from "@/components/main/Sidebar";

const Home: React.FC = () => {
  return (
    <div className="bg-gray-100 text-gray-800 shadow-md">
      <div className="container mx-auto px-4 py-6">
        <TopBanner />
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <MainFeature />
            <LatestNewsPosts />
            <PromoInvestasi />
          </div>
          <div className="lg:col-span-1">
            <Sidebar />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
