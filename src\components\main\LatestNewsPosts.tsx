// src/components/LatestNewsPosts.tsx
import React from "react";

interface NewsItem {
  id: number;
  title: string;
  image: string;
  date: string;
}

const LatestNewsPosts: React.FC = () => {
  const newsItems: NewsItem[] = [
    {
      id: 1,
      title: "2024 Investasi di Pekanbaru Serap Belasan Ribu Tenaga Kerja",
      image: "/berita/mainfeat.jpg",
      date: "20 Mar 2025",
    },
    {
      id: 2,
      title:
        "Kepala DPMPTSP Pekanbaru Ikuti A<PERSON> da<PERSON>alikot<PERSON>",
      image: "/berita/2.jpeg",
      date: "19 Jul 2024",
    },
    {
      id: 3,
      title:
        "PJ Wali Kota Pekanbaru Tinjau Pelayanan MPP Pasca Beroperasi Hingga di DPMPTSP",
      image: "/berita/3.jpeg",
      date: "18 Jul 2024",
    },
    {
      id: 4,
      title:
        "Satgas Saber Pungli Provinsi Riau Sosialisasikan Aplikasi Si Dali ke DPMPTSP Kota Pekanbaru",
      image: "/berita/4.jpg",
      date: "10 Apr 2024",
    },
    {
      id: 5,
      title:
        "Ratusan Pengikba Rumah Makan Non Muslim Ajukan Izin Operasional selama Ramadan",
      image: "/berita/5.jpg",
      date: "26 Mar 2024",
    },
    {
      id: 6,
      title: "DPMPTSP Pekanbaru Telah Berikan Izin Operasi 94 RM Non Muslim",
      image: "/berita/6.jpeg",
      date: "20 Mar 2024",
    },
  ];

  return (
    <div className="mt-8">
      <h3 className="text-lg font-bold border-b-2 border-gray-200 pb-2 mb-4">
        LATEST NEWS POSTS
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="col-span-1">
          <div className="bg-white shadow-md rounded overflow-hidden">
            <img
              src="/berita/mainfeat.jpg"
              alt="News"
              className="w-full h-48 object-cover"
            />
            <div className="p-4">
              <h4 className="text-md font-bold mb-2">
                2024 Investasi di Pekanbaru Serap Belasan Ribu Tenaga Kerja
              </h4>
              <button className="bg-red-600 text-white px-3 py-1 rounded text-sm">
                Read More
              </button>
            </div>
          </div>
        </div>
        <div className="col-span-1">
          <div className="space-y-4">
            {newsItems.slice(1).map((item) => (
              <div
                key={item.id}
                className="flex bg-white shadow-sm rounded overflow-hidden"
              >
                <div className="w-24 h-20 flex-shrink-0">
                  <img
                    src={item.image}
                    alt={item.title}
                    className="w-24 h-20 object-cover"
                    style={{ width: "96px", height: "80px" }}
                  />
                </div>
                <div className="p-2 flex-1">
                  <p className="text-xs text-gray-500">{item.date}</p>
                  <h5 className="text-sm font-medium">{item.title}</h5>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LatestNewsPosts;
