import React from "react";

interface InvestmentItem {
  id: number;
  image: string;
  title: string;
  link: string;
}

const PromoInvestasi: React.FC = () => {
  const investmentItems: InvestmentItem[] = [
    {
      id: 1,
      image: "/investasi/i1.jpg",
      title:
        "Semester <PERSON>tama 2024, Investasi yang Masuk ke Pekanbaru Capai Rp2,978 Triliun",
      link: "/investasi/semester-pertama-2024",
    },
    {
      id: 2,
      image: "/investasi/i2.jpg",
      title:
        "Investasi PMDN dan PMA Pekanbaru di TW II Serap 4,074 Tenaga Kerja",
      link: "/investasi/pmdn-pma-tw2",
    },
    {
      id: 3,
      image: "/investasi/i3.jpeg",
      title: "Realisasi Investasi Pekanbaru Triwulan II Capai Rp 27,8 Triliun",
      link: "/investasi/realisasi-tw2",
    },
  ];

  return (
    <div className="mt-8">
      <h2 className="font-bold border-b pb-2 mb-4">
        PROMOSI INVESTASI
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {investmentItems.map((item) => (
          <div
            key={item.id}
            className="bg-white shadow-md rounded overflow-hidden"
          >
            <img
              src={item.image}
              alt={item.title}
              className="w-full h-48 object-cover"
            />
            <div className="p-4">
              <h3 className="font-semibold text-sm text-center hover:text-green-700">
                <a href={item.link} className="hover:underline">
                  {item.title}
                </a>
              </h3>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PromoInvestasi;
